{"actor_host": "ImpalaHostActor", "actor_worker": "ImpalaWorkerActor", "ceil": 1, "custom_network": "RealNetwork", "discount": 0.99, "entropy_weight": 0.01, "env": "EnvUCS-v0", "epoch_len": 500000, "eval": false, "exp": "Rollout", "floor": -1, "fourconv_norm": "bn", "frame_stack": false, "grad_norm_clip": "0.5", "head1d": "Identity1D", "head2d": "Identity2D", "head3d": "Identity3D", "head4d": "Identity4D", "learner": "ImpalaLearner", "load_network": null, "load_optim": null, "logdir": "/home/<USER>/wh/logs", "lr": 0.0007, "lstm_nb_hidden": 256, "lstm_normalize": true, "manager": "SubProcEnvManager", "max_episode_length": 10000, "minimum_importance_policy": 1.0, "minimum_importance_value": 1.0, "nb_env": 8, "nb_learn_batch": 8, "nb_learners": 1, "nb_step": 100000000, "nb_workers": 4, "net1d": "Identity1D", "net2d": "Identity2D", "net3d": "FourConv", "net4d": "Identity4D", "netbody": "Linear", "nb_layer": 2, "linear_nb_hidden": 256, "noop_max": 30, "profile": false, "prompt": false, "ray_addr": null, "resume": null, "rollout_len": 20, "rollout_queue_size": 20, "rwd_norm": "Clip", "skip_rate": 4, "summary_freq": 10, "tag": "sf_new_rnd", "learner_cpu_alloc": 2, "learner_gpu_alloc": 0.5, "worker_cpu_alloc": 2, "worker_gpu_alloc": 0.05, "cuda_visible_device": "3", "independent_reward": true, "shared_params": true, "concat_obs": true, "bg": 2, "n_layers": 2, "use_transformer": true, "use_intrinsic": true, "intrinsic_coef": 0.3, "dataset": "sf", "test_mode": false, "nb_agent": 3, "description": "test"}