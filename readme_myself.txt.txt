


重新启动云服务器环境的完整步骤

1. 重新连接并激活环境
# 激活conda环境
conda activate ucs
# 进入项目目录
cd ~/autodl-tmp/DRL-UCS-AoI-Th/DRL_UCS_AoI/adept/scripts

2. 设置必要的环境变量
# 设置CUDA和Ray环境变量
export CUDA_VISIBLE_DEVICES=0
export RAY_OBJECT_STORE_ALLOW_SLOW_STORAGE=1

3. 验证环境状态
# 检查GPU状态
nvidia-smi

# 验证PyTorch CUDA
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}')"

# 验证核心模块导入
python -c "
try:
    from adept.env import EnvUCS
    from adept.agent import ActorCritic
    from adept.learner import ImpalaLearner
    print('✅ 所有核心模块导入成功')
except Exception as e:
    print(f'❌ 模块导入失败: {e}')
"

4. 应用之前的修复（如果需要）

# 检查Ray API修复是否还在
grep -n "num_returns=len(self.futures)" ~/autodl-tmp/DRL-UCS-AoI-Th/DRL_UCS_AoI/adept/container/actorlearner/rollout_queuer.py

# 如果没有找到，重新应用修复
sed -i 's/self.futures, len(self.futures), timeout=self.queue_timeout/self.futures, num_returns=len(self.futures), timeout=self.queue_timeout/' ~/autodl-tmp/DRL-UCS-AoI-Th/DRL_UCS_AoI/adept/container/actorlearner/rollout_queuer.py

5. 创建修正的配置文件
# 创建GPU配置修正的配置文件
python -c "
import json
with open('../config/bj.json', 'r') as f:
    config = json.load(f)

# 修改关键配置
config['cuda_visible_device'] = '0'  # 使用GPU 0
config['logdir'] = '/root/autodl-tmp/DRL-UCS-AoI-Th/logs'  # 可写日志路径

with open('../config/bj_fixed.json', 'w') as f:
    json.dump(config, f, indent=2)

print('✅ 配置文件已创建: bj_fixed.json')
"

# 创建日志目录
mkdir -p /root/autodl-tmp/DRL-UCS-AoI-Th/logs

6. 运行测试验证

# 先运行短测试验证环境
python actorlearner.py --config ../config/bj_fixed.json --nb-step 1000 --tag test_restart

7. 如果测试成功，运行完整训练

# 运行完整训练
python actorlearner.py --config ../config/bj_fixed.json --tag beijing_full_training


快速一键脚本
# 创建一键启动脚本
cat > ~/restart_training.sh << 'EOF'
#!/bin/bash
echo "🚀 重新启动DRL-UCS-AoI训练环境..."

# 激活环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate ucs

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export RAY_OBJECT_STORE_ALLOW_SLOW_STORAGE=1

# 进入项目目录
cd ~/autodl-tmp/DRL-UCS-AoI-Th/DRL_UCS_AoI/adept/scripts

# 验证环境
echo "📋 验证环境状态..."
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# 应用Ray修复
sed -i 's/self.futures, len(self.futures), timeout=self.queue_timeout/self.futures, num_returns=len(self.futures), timeout=self.queue_timeout/' ~/autodl-tmp/DRL-UCS-AoI-Th/DRL_UCS_AoI/adept/container/actorlearner/rollout_queuer.py

# 创建配置文件
python -c "
import json
with open('../config/bj.json', 'r') as f:
    config = json.load(f)
config['cuda_visible_device'] = '0'
config['logdir'] = '/root/autodl-tmp/DRL-UCS-AoI-Th/logs'
with open('../config/bj_fixed.json', 'w') as f:
    json.dump(config, f, indent=2)
print('✅ 环境重启完成!')
"

mkdir -p /root/autodl-tmp/DRL-UCS-AoI-Th/logs

echo "🎯 环境已准备就绪，可以运行训练了!"
echo "测试命令: python actorlearner.py --config ../config/bj_fixed.json --nb-step 1000 --tag test"
echo "完整训练: python actorlearner.py --config ../config/bj_fixed.json --tag beijing_training"
EOF

# 使脚本可执行
chmod +x ~/restart_training.sh

# 运行脚本
~/restart_training.sh

