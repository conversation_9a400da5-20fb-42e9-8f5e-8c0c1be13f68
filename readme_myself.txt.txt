


重新启动云服务器环境的完整步骤

1. 重新连接并激活环境
# 激活conda环境
conda activate ucs
# 进入项目目录
cd ~/autodl-tmp/DRL-UCS-AoI-Th/DRL_UCS_AoI/adept/scripts

2. 设置必要的环境变量
# 设置CUDA和Ray环境变量
export CUDA_VISIBLE_DEVICES=0
export RAY_OBJECT_STORE_ALLOW_SLOW_STORAGE=1

3. 验证环境状态
# 检查GPU状态
nvidia-smi

# 验证PyTorch CUDA
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}')"

# 验证核心模块导入
python -c "
try:
    from adept.env import EnvUCS
    from adept.agent import ActorCritic
    from adept.learner import ImpalaLearner
    print('✅ 所有核心模块导入成功')
except Exception as e:
    print(f'❌ 模块导入失败: {e}')
"